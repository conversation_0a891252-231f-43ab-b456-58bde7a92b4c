<?php
header('Content-Type: application/json');
$conn = new mysqli("localhost", "root", "", "ananta_task");
if ($conn->connect_error) {
    echo json_encode(["data" => [], "error" => "Connection failed: " . $conn->connect_error]);
    exit;
}
$sql = "SELECT id, name, email, image FROM tasks ORDER BY id DESC";
$result = $conn->query($sql);
$data = [];
if ($result && $result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $row['image'] = $row['image'] ? '<img src="uploads/' . htmlspecialchars($row['image']) . '" style="max-width:80px;max-height:80px;" />' : 'No image';
        $data[] = $row;
    }
}
echo json_encode(["data" => $data]);
// Debugging: If no data, output error
if (empty($data)) {
    // Uncomment the next line to debug in browser/console
    // error_log('No data returned from tasks table.');
}
