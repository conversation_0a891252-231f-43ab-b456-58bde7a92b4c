<html>
<head>
  <title>Task</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.3/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
</head>
<body>
  <div class="container">
    <div class="row mb-2"> 
      <div class="col-md-12">
        <h1>Task</h1>
        <p>Task is a simple task management system.</p>
        <a href="add.php" class="btn btn-primary">Add Task</a>
      </div>
    </div>


    <div class="row">
      <div class="col-md-12">
        <table id="tasksTable" class="table table-striped table-bordered table-hover table-responsive datatable" style="width:100%">
          <thead>
            <tr>
              <th>ID</th>
              <th>Name</th>
              <th>Email</th>
              <th>Image</th>
              <th>Action</th>
            </tr>
          </thead>
        </table>
      </div>
    </div>
  </div>

<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.3/js/bootstrap.bundle.min.js"></script>
<script>
  $(document).ready(function() {
    $('#tasksTable').DataTable({
      "processing": true,
      "ajax": {
        "url": "tasks_api.php",
        "dataSrc": "data"
      },
      "columns": [
        { "data": "id" },
        { "data": "name" },
        { "data": "email" },
        { 
          "data": "image",
          "orderable": false,
          "render": function(data, type, row) {
            // DataTables expects HTML for image, but only for display
            if (type === 'display' || type === 'filter') return data;
            return row.image;
          }
        },
        {
          "data": null,
          "orderable": false,
          "render": function (data, type, row) {
            return '<button class="btn btn-sm btn-warning editBtn" data-id="'+row.id+'">Edit</button> ' +
                   '<button class="btn btn-sm btn-danger deleteBtn" data-id="'+row.id+'">Delete</button>';
          }
        }
      ]
    });

    // Delete
    $('#tasksTable').on('click', '.deleteBtn', function() {
      if(confirm('Are you sure you want to delete this task?')) {
        var id = $(this).data('id');
        $.post('task_crud.php', {action: 'delete', id: id}, function(resp) {
          if(resp.status === 'success') {
            $('#tasksTable').DataTable().ajax.reload();
          } else {
            alert(resp.message || 'Delete failed');
          }
        }, 'json');
      }
    });

    // Edit
    $('#tasksTable').on('click', '.editBtn', function() {
      var id = $(this).data('id');
      $.post('task_crud.php', {action: 'edit', id: id}, function(resp) {
        if(resp.status === 'success') {
          var data = resp.data;
          $('#editId').val(data.id);
          $('#editName').val(data.name);
          $('#editEmail').val(data.email);
          $('#editModal').modal('show');
        } else {
          alert(resp.message || 'Edit failed');
        }
      }, 'json');
    });

    // Update
    $('#editForm').on('submit', function(e) {
      e.preventDefault();
      var formData = new FormData(this);
      formData.append('action', 'update');
      $.ajax({
        url: 'task_crud.php',
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        dataType: 'json',
        success: function(resp) {
          if(resp.status === 'success') {
            $('#editModal').modal('hide');
            $('#tasksTable').DataTable().ajax.reload();
          } else {
            alert(resp.message || 'Update failed');
          }
        }
      });
    });
  });
<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <form id="editForm" enctype="multipart/form-data">
        <div class="modal-header">
          <h5 class="modal-title" id="editModalLabel">Edit Task</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <input type="hidden" name="id" id="editId">
          <div class="mb-3">
            <label for="editName" class="form-label">Name</label>
            <input type="text" class="form-control" name="name" id="editName" required>
          </div>
          <div class="mb-3">
            <label for="editEmail" class="form-label">Email</label>
            <input type="email" class="form-control" name="email" id="editEmail" required>
          </div>
          <div class="mb-3">
            <label for="editImage" class="form-label">Image (leave blank to keep current)</label>
            <input type="file" class="form-control" name="image" id="editImage">
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <button type="submit" class="btn btn-primary">Update</button>
        </div>
      </form>
    </div>
  </div>
</div>

</body>
</html>