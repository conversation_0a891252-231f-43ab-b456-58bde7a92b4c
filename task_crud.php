<?php
header('Content-Type: application/json');
$conn = new mysqli("localhost", "root", "", "ananta_task");
if ($conn->connect_error) {
    echo json_encode(["status" => "error", "message" => "Connection failed: " . $conn->connect_error]);
    exit;
}
$action = $_POST['action'] ?? '';
if ($action === 'delete') {
    $id = intval($_POST['id'] ?? 0);
    $sql = "DELETE FROM tasks WHERE id = $id";
    if ($conn->query($sql)) {
        echo json_encode(["status" => "success"]);
    } else {
        echo json_encode(["status" => "error", "message" => $conn->error]);
    }
    exit;
}
if ($action === 'edit') {
    $id = intval($_POST['id'] ?? 0);
    $sql = "SELECT * FROM tasks WHERE id = $id";
    $result = $conn->query($sql);
    if ($result && $result->num_rows > 0) {
        echo json_encode(["status" => "success", "data" => $result->fetch_assoc()]);
    } else {
        echo json_encode(["status" => "error", "message" => "Task not found"]);
    }
    exit;
}
if ($action === 'update') {
    $id = intval($_POST['id'] ?? 0);
    $name = $conn->real_escape_string($_POST['name'] ?? '');
    $email = $conn->real_escape_string($_POST['email'] ?? '');
    $imageName = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
        $imageName = uniqid() . '_' . basename($_FILES['image']['name']);
        $targetDir = "uploads/";
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0777, true);
        }
        $targetFile = $targetDir . $imageName;
        move_uploaded_file($_FILES['image']['tmp_name'], $targetFile);
        $imgSql = ", image='$imageName'";
    } else {
        $imgSql = '';
    }
    $sql = "UPDATE tasks SET name='$name', email='$email' $imgSql WHERE id=$id";
    if ($conn->query($sql)) {
        echo json_encode(["status" => "success"]);
    } else {
        echo json_encode(["status" => "error", "message" => $conn->error]);
    }
    exit;
}
echo json_encode(["status" => "error", "message" => "Invalid action"]);
