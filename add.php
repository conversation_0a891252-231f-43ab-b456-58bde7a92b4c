<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax'])) {
    $conn = new mysqli("localhost", "root", "", "ananta_task");
    if ($conn->connect_error) {
        echo json_encode(["status" => "error", "message" => "Connection failed: " . $conn->connect_error]);
        exit;
    }
    $name  = $conn->real_escape_string($_POST['name']);
    $email = $conn->real_escape_string($_POST['email']);

    $imageName = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
        $imageName = uniqid() . '_' . basename($_FILES['image']['name']);
        $targetDir = "uploads/";
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0777, true);
        }
        $targetFile = $targetDir . $imageName;
        move_uploaded_file($_FILES['image']['tmp_name'], $targetFile);
    }

    $created_at = date('Y-m-d H:i:s');
    $sql = "INSERT INTO tasks (name, email, image, created_at) VALUES ('$name', '$email', '$imageName', '$created_at')";
    if ($conn->query($sql) === TRUE) {
        echo json_encode(["status" => "success", "message" => "Record added successfully."]);
    } else {
        echo json_encode(["status" => "error", "message" => "Error: " . $conn->error]);
    }
    exit;
}
$successMsg = $errorMsg = "";
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">  
  <title>Add Taskss</title>
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
</head>
<body>
<div class="container mt-5">
  <h2>Impoo</h2>
  <div id="responseMsg"></div>
  <form id="taskForm" enctype="multipart/form-data" autocomplete="off">
    <div class="form-group">
      <label for="name">Name:</label>
      <input type="text" class="form-control alphabetsOnly" placeholder="Enter name" id="name" name="name" required>
    </div>
    <div class="form-group">
      <label for="email">Email:</label>
      <input type="email" class="form-control" id="email" name="email" placeholder="Enter email" required>
    </div>
    <div class="form-group">
      <label for="pwd">Image:</label>
      <input type="file" class="form-control" id="image" name="image" required>
    </div>
    <button type="submit" class="btn btn-primary">Submit</button>
    <a href="index.php" class="btn btn-secondary">Back</a>
  </form>
  <script>
    $(document).ready(function() {
      $('#taskForm').on('submit', function(e) {
        e.preventDefault();
        var formData = new FormData(this);
        formData.append('ajax', '1');
        $.ajax({
          url: 'add.php',
          type: 'POST',
          data: formData,
          contentType: false,
          processData: false,
          dataType: 'json',
          success: function(response) {
            if(response.status === 'success') {
              $('#responseMsg').html('<div class="alert alert-success">'+response.message+'</div>');
              $('#taskForm')[0].reset();
            } else {
              $('#responseMsg').html('<div class="alert alert-danger">'+response.message+'</div>');
            }
          },
          error: function() {
            $('#responseMsg').html('<div class="alert alert-danger">AJAX error occurred.</div>');
          }
        });
      });
    });
  </script>
</div>
</body>
</html>


